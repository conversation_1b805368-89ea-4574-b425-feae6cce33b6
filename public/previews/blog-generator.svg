<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0a0a0a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0a0a0a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#262626;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#171717;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bgGradient)"/>
  
  <!-- Ambient glow effects -->
  <circle cx="90" cy="80" r="50" fill="url(#brandGradient)" opacity="0.1" filter="blur(25px)"/>
  <circle cx="310" cy="200" r="40" fill="url(#brandGradient)" opacity="0.08" filter="blur(20px)"/>
  
  <!-- Main content area -->
  <rect x="20" y="20" width="360" height="260" rx="12" fill="url(#cardGradient)" stroke="rgba(251, 191, 36, 0.2)" stroke-width="1"/>
  
  <!-- Header -->
  <rect x="40" y="40" width="320" height="35" rx="8" fill="rgba(0,0,0,0.3)"/>
  <rect x="50" y="48" width="6" height="8" fill="url(#brandGradient)" rx="1"/>
  <rect x="58" y="48" width="8" height="6" fill="url(#brandGradient)" rx="1"/>
  <rect x="58" y="56" width="6" height="6" fill="url(#brandGradient)" rx="1"/>
  <text x="75" y="62" fill="#ffffff" font-family="Arial, sans-serif" font-size="13" font-weight="bold">AI Blog Generator</text>
  
  <!-- Content creation flow -->
  <!-- Topic Input -->
  <rect x="50" y="90" width="120" height="30" rx="6" fill="rgba(255,255,255,0.1)"/>
  <text x="60" y="105" fill="#e5e5e5" font-family="Arial, sans-serif" font-size="9">Topic: "AI in Healthcare"</text>
  <text x="60" y="115" fill="rgba(255,255,255,0.6)" font-family="Arial, sans-serif" font-size="8">Keywords: medical, automation</text>
  
  <!-- AI Processing -->
  <g transform="translate(190, 90)">
    <rect x="0" y="0" width="80" height="30" rx="6" fill="rgba(251, 191, 36, 0.15)" stroke="rgba(251, 191, 36, 0.3)"/>
    <text x="40" y="18" fill="url(#brandGradient)" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" font-weight="bold">GPT-4</text>
    <text x="40" y="26" fill="url(#brandGradient)" font-family="Arial, sans-serif" font-size="7" text-anchor="middle">Processing...</text>
    
    <!-- Processing dots -->
    <circle cx="20" cy="8" r="1.5" fill="url(#brandGradient)" opacity="0.6">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="30" cy="8" r="1.5" fill="url(#brandGradient)" opacity="0.4">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="1.2s" begin="0.3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="40" cy="8" r="1.5" fill="url(#brandGradient)" opacity="0.2">
      <animate attributeName="opacity" values="0.2;0.6;0.2" dur="1.2s" begin="0.6s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Generated Article -->
  <rect x="50" y="140" width="300" height="80" rx="6" fill="rgba(0,0,0,0.4)" stroke="rgba(255,255,255,0.1)"/>
  <text x="60" y="155" fill="url(#brandGradient)" font-family="Arial, sans-serif" font-size="11" font-weight="bold">The Future of AI in Healthcare: Transforming Patient Care</text>
  
  <!-- Article content lines -->
  <rect x="60" y="165" width="280" height="2" fill="rgba(255,255,255,0.3)" rx="1"/>
  <rect x="60" y="172" width="250" height="2" fill="rgba(255,255,255,0.2)" rx="1"/>
  <rect x="60" y="179" width="270" height="2" fill="rgba(255,255,255,0.2)" rx="1"/>
  <rect x="60" y="186" width="200" height="2" fill="rgba(255,255,255,0.2)" rx="1"/>
  
  <!-- SEO indicators -->
  <g transform="translate(60, 195)">
    <rect x="0" y="0" width="40" height="15" rx="7" fill="rgba(34, 197, 94, 0.2)"/>
    <text x="20" y="9" fill="#22c55e" font-family="Arial, sans-serif" font-size="7" text-anchor="middle">SEO ✓</text>
  </g>
  
  <g transform="translate(110, 195)">
    <rect x="0" y="0" width="50" height="15" rx="7" fill="rgba(34, 197, 94, 0.2)"/>
    <text x="25" y="9" fill="#22c55e" font-family="Arial, sans-serif" font-size="7" text-anchor="middle">1200 words</text>
  </g>
  
  <g transform="translate(170, 195)">
    <rect x="0" y="0" width="45" height="15" rx="7" fill="rgba(34, 197, 94, 0.2)"/>
    <text x="22.5" y="9" fill="#22c55e" font-family="Arial, sans-serif" font-size="7" text-anchor="middle">Score: 95</text>
  </g>
  
  <!-- Publishing platforms -->
  <text x="50" y="240" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Auto-publish to:</text>
  
  <g transform="translate(50, 245)">
    <rect x="0" y="0" width="50" height="16" rx="8" fill="rgba(255,255,255,0.1)"/>
    <text x="25" y="10" fill="#ffffff" font-family="Arial, sans-serif" font-size="7" text-anchor="middle">WordPress</text>
  </g>
  
  <g transform="translate(110, 245)">
    <rect x="0" y="0" width="40" height="16" rx="8" fill="rgba(255,255,255,0.1)"/>
    <text x="20" y="10" fill="#ffffff" font-family="Arial, sans-serif" font-size="7" text-anchor="middle">Medium</text>
  </g>
  
  <g transform="translate(160, 245)">
    <rect x="0" y="0" width="45" height="16" rx="8" fill="rgba(255,255,255,0.1)"/>
    <text x="22.5" y="10" fill="#ffffff" font-family="Arial, sans-serif" font-size="7" text-anchor="middle">LinkedIn</text>
  </g>
  
  <!-- Calendar icon -->
  <g transform="translate(320, 90)">
    <rect x="0" y="0" width="24" height="24" rx="3" fill="rgba(249, 115, 22, 0.2)" stroke="rgba(249, 115, 22, 0.4)"/>
    <rect x="3" y="8" width="18" height="2" fill="#f97316"/>
    <rect x="6" y="12" width="3" height="2" fill="#f97316"/>
    <rect x="12" y="12" width="3" height="2" fill="#f97316"/>
    <rect x="18" y="12" width="3" height="2" fill="#f97316"/>
    <text x="12" y="22" fill="#f97316" font-family="Arial, sans-serif" font-size="6" text-anchor="middle">Schedule</text>
  </g>
  
  <!-- Category badge -->
  <rect x="280" y="30" width="100" height="18" rx="9" fill="rgba(251, 191, 36, 0.2)"/>
  <text x="330" y="41" fill="url(#brandGradient)" font-family="Arial, sans-serif" font-size="8" text-anchor="middle" font-weight="bold">CONTENT</text>
</svg>
