<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0a0a0a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0a0a0a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#262626;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#171717;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bgGradient)"/>
  
  <!-- Ambient glow effects -->
  <circle cx="100" cy="80" r="60" fill="url(#brandGradient)" opacity="0.1" filter="blur(20px)"/>
  <circle cx="320" cy="220" r="40" fill="url(#brandGradient)" opacity="0.08" filter="blur(15px)"/>
  
  <!-- Main content area -->
  <rect x="20" y="20" width="360" height="260" rx="12" fill="url(#cardGradient)" stroke="rgba(251, 191, 36, 0.2)" stroke-width="1"/>
  
  <!-- Header -->
  <rect x="40" y="40" width="320" height="40" rx="8" fill="rgba(0,0,0,0.3)"/>
  <circle cx="60" cy="60" r="8" fill="url(#brandGradient)"/>
  <text x="80" y="66" fill="#ffffff" font-family="Arial, sans-serif" font-size="14" font-weight="bold">AI Customer Support</text>
  
  <!-- Chat bubbles -->
  <!-- Customer message -->
  <rect x="60" y="100" width="200" height="30" rx="15" fill="rgba(255,255,255,0.1)"/>
  <text x="70" y="118" fill="#e5e5e5" font-family="Arial, sans-serif" font-size="11">Hi, I need help with my order</text>
  
  <!-- AI response -->
  <rect x="140" y="140" width="220" height="45" rx="15" fill="url(#brandGradient)" opacity="0.8"/>
  <text x="150" y="158" fill="#ffffff" font-family="Arial, sans-serif" font-size="11" font-weight="500">I'd be happy to help! Let me</text>
  <text x="150" y="172" fill="#ffffff" font-family="Arial, sans-serif" font-size="11" font-weight="500">check your order status...</text>
  
  <!-- Processing indicator -->
  <circle cx="80" cy="200" r="3" fill="url(#brandGradient)" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="95" cy="200" r="3" fill="url(#brandGradient)" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="110" cy="200" r="3" fill="url(#brandGradient)" opacity="0.2">
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Integration icons -->
  <g transform="translate(280, 200)">
    <!-- Zendesk icon placeholder -->
    <rect x="0" y="0" width="24" height="24" rx="4" fill="rgba(255,255,255,0.1)"/>
    <text x="12" y="16" fill="#ffffff" font-family="Arial, sans-serif" font-size="8" text-anchor="middle">ZD</text>
  </g>
  
  <g transform="translate(310, 200)">
    <!-- Slack icon placeholder -->
    <rect x="0" y="0" width="24" height="24" rx="4" fill="rgba(255,255,255,0.1)"/>
    <text x="12" y="16" fill="#ffffff" font-family="Arial, sans-serif" font-size="8" text-anchor="middle">SL</text>
  </g>
  
  <!-- Bottom stats -->
  <text x="40" y="250" fill="rgba(255,255,255,0.6)" font-family="Arial, sans-serif" font-size="10">24/7 Support • AI-Powered • Multi-Platform</text>
  
  <!-- Category badge -->
  <rect x="300" y="30" width="80" height="20" rx="10" fill="rgba(251, 191, 36, 0.2)"/>
  <text x="340" y="42" fill="url(#brandGradient)" font-family="Arial, sans-serif" font-size="9" text-anchor="middle" font-weight="bold">CHATBOT</text>
</svg>
