'use client'

import { Suspense, lazy, useEffect, useRef, useState } from 'react'
const Spline = lazy(() => import('@splinetool/react-spline'))

interface SplineSceneProps {
  scene: string
  className?: string
}

export function SplineScene({ scene, className }: SplineSceneProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Delay loading slightly to improve perceived performance
          setTimeout(() => setShouldLoad(true), 100);
        } else {
          setIsVisible(false);
        }
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div
      ref={containerRef}
      className={`w-full h-full ${className || ''}`}
      style={{
        willChange: 'transform',
        transform: 'translateZ(0)',
        backfaceVisibility: 'hidden',
      }}
    >
      {shouldLoad ? (
        <Suspense
          fallback={
            <div className="w-full h-full flex items-center justify-center bg-black/20 backdrop-blur-sm">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
            </div>
          }
        >
          <Spline
            scene={scene}
            className="w-full h-full"
            style={{
              willChange: isVisible ? 'transform' : 'auto',
              transform: 'translateZ(0)',
            }}
          />
        </Suspense>
      ) : (
        <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-black/40 to-black/60 backdrop-blur-sm">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-yellow-400/20 via-orange-500/20 to-red-500/20 flex items-center justify-center">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 animate-pulse"></div>
            </div>
            <p className="text-sm text-neutral-400">Loading 3D Scene...</p>
          </div>
        </div>
      )}
    </div>
  )
}