'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Mail, Calendar, Send, Building, DollarSign, Phone } from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"

export function ContactSection() {
  const [formData, setFormData] = useState({
    businessName: '',
    contactName: '',
    email: '',
    phone: '',
    industry: '',
    currentProcesses: '',
    budgetRange: '',
    contactMethod: '',
    projectDescription: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Using Formspree for form submission
      const response = await fetch('https://formspree.io/f/xpwzgqpb', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessName: formData.businessName,
          contactName: formData.contactName,
          email: formData.email,
          phone: formData.phone,
          industry: formData.industry,
          currentProcesses: formData.currentProcesses,
          budgetRange: formData.budgetRange,
          contactMethod: formData.contactMethod,
          _subject: `New Automation Inquiry from ${formData.businessName}`,
        }),
      })

      if (response.ok) {
        toast.success('Thank you! We\'ll be in touch within 24 hours.')
        setFormData({
          businessName: '',
          contactName: '',
          email: '',
          phone: '',
          industry: '',
          currentProcesses: '',
          budgetRange: '',
          contactMethod: '',
          projectDescription: ''
        })
      } else {
        throw new Error('Form submission failed')
      }
    } catch (error) {
      toast.error('Something went wrong. Please try again or email us directly.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <section id="contact" className="py-20 md:py-28 lg:py-36 xl:py-44 bg-black/[0.96] relative w-full overflow-hidden">
      {/* Optimized ambient background lighting */}
      <div className="absolute inset-0 ambient-gradient-overlay opacity-50 performance-optimized" />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-yellow-400/3 via-orange-500/2 to-red-500/2 rounded-full blur-3xl animate-pulse performance-optimized" style={{ animationDuration: '8s' }} />

      <div className="container mx-auto relative z-10 max-w-6xl">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-start">
          {/* Left side - Headline and CTA */}
          <div className="flex flex-col justify-center">
            {/* Headline */}
            <div className="mb-8 lg:mb-12">
              <h2 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 block">
                  Ready to Transform Your Business?
                </span>
              </h2>
            </div>

            {/* Description */}
            <div className="mb-10 lg:mb-14">
              <p className="text-lg md:text-xl lg:text-2xl text-neutral-300 leading-relaxed">
                Get a free consultation to discover how AI automation can streamline your operations, 
                reduce costs, and boost productivity. Our experts will design a custom solution for your business.
              </p>
            </div>

            {/* Quick Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 lg:gap-8">
              <Button
                size="lg"
                className="text-white hover:text-white font-semibold text-lg md:text-xl px-8 md:px-12 py-4 md:py-6 transition-all duration-300 border-2 border-orange-500/70 hover:border-orange-500 backdrop-blur-sm bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-300 hover:via-orange-400 hover:to-red-400 shadow-lg hover:shadow-xl ring-1 ring-white/20 hover:ring-white/30 brand-glow-hover hover:scale-105 active:scale-95"
                asChild
              >
                <Link href="https://calendly.com/sharpflow" target="_blank">
                  <Calendar className="mr-3 w-5 h-5" />
                  Book Free Call
                </Link>
              </Button>
              <Button
                size="lg"
                variant="ghost"
                className="text-white hover:text-white font-semibold text-lg md:text-xl px-8 md:px-12 py-4 md:py-6 transition-all duration-300 border-2 border-white/30 hover:border-orange-500/60 backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 hover:from-orange-500/20 hover:to-red-500/30 shadow-sm hover:shadow-md brand-glow-hover hover:scale-105 active:scale-95"
                asChild
              >
                <Link href="mailto:<EMAIL>">
                  <Mail className="mr-3 w-5 h-5" />
                  Send Email
                </Link>
              </Button>
            </div>
          </div>

          {/* Right side - Contact Form */}
          <div className="relative">
            <Card className="bg-black/40 backdrop-blur-sm border border-neutral-800/50 shadow-2xl">
              <CardContent className="p-8">
                <div className="mb-6">
                  <h3 className="text-2xl font-semibold text-white mb-2">Get Your Custom Quote</h3>
                  <p className="text-neutral-400">Tell us about your business and automation needs</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="businessName" className="text-neutral-300">Business Name *</Label>
                      <Input
                        id="businessName"
                        value={formData.businessName}
                        onChange={(e) => handleInputChange('businessName', e.target.value)}
                        className="bg-neutral-900/50 border-neutral-700 text-white"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contactName" className="text-neutral-300">Contact Name *</Label>
                      <Input
                        id="contactName"
                        value={formData.contactName}
                        onChange={(e) => handleInputChange('contactName', e.target.value)}
                        className="bg-neutral-900/50 border-neutral-700 text-white"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-neutral-300">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="bg-neutral-900/50 border-neutral-700 text-white"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-neutral-300">Phone</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="bg-neutral-900/50 border-neutral-700 text-white"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="industry" className="text-neutral-300">Industry *</Label>
                      <Select value={formData.industry} onValueChange={(value) => handleInputChange('industry', value)}>
                        <SelectTrigger className="bg-neutral-900/50 border-neutral-700 text-white">
                          <SelectValue placeholder="Select your industry" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ecommerce">E-commerce</SelectItem>
                          <SelectItem value="saas">SaaS</SelectItem>
                          <SelectItem value="healthcare">Healthcare</SelectItem>
                          <SelectItem value="finance">Finance</SelectItem>
                          <SelectItem value="real-estate">Real Estate</SelectItem>
                          <SelectItem value="manufacturing">Manufacturing</SelectItem>
                          <SelectItem value="consulting">Consulting</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="budgetRange" className="text-neutral-300">Budget Range</Label>
                      <Select value={formData.budgetRange} onValueChange={(value) => handleInputChange('budgetRange', value)}>
                        <SelectTrigger className="bg-neutral-900/50 border-neutral-700 text-white">
                          <SelectValue placeholder="Select budget range" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="under-5k">Under $5,000</SelectItem>
                          <SelectItem value="5k-15k">$5,000 - $15,000</SelectItem>
                          <SelectItem value="15k-50k">$15,000 - $50,000</SelectItem>
                          <SelectItem value="50k-plus">$50,000+</SelectItem>
                          <SelectItem value="discuss">Let's discuss</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="currentProcesses" className="text-neutral-300">Current Processes to Automate *</Label>
                    <Textarea
                      id="currentProcesses"
                      value={formData.currentProcesses}
                      onChange={(e) => handleInputChange('currentProcesses', e.target.value)}
                      placeholder="Describe the business processes you'd like to automate..."
                      className="bg-neutral-900/50 border-neutral-700 text-white min-h-[100px]"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactMethod" className="text-neutral-300">Preferred Contact Method</Label>
                    <Select value={formData.contactMethod} onValueChange={(value) => handleInputChange('contactMethod', value)}>
                      <SelectTrigger className="bg-neutral-900/50 border-neutral-700 text-white">
                        <SelectValue placeholder="How would you like us to contact you?" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="phone">Phone Call</SelectItem>
                        <SelectItem value="video">Video Call</SelectItem>
                        <SelectItem value="in-person">In-Person Meeting</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full text-white hover:text-white font-semibold text-lg py-6 transition-all duration-300 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-300 hover:via-orange-400 hover:to-red-400 shadow-lg hover:shadow-xl hover:shadow-orange-500/25 border border-white/10 hover:border-white/20 hover:scale-[1.02] active:scale-[0.98]"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="mr-3 w-5 h-5" />
                        Get Free Consultation
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
