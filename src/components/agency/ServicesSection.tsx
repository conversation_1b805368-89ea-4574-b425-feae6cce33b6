"use client";

import { Bot, Workflow, Zap, Database, Mail, Calendar, FileText, Target, ShoppingCart, Users, BarChart3, Plug } from "lucide-react";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import { cn } from "@/lib/utils";

// Service data for the agency
const services = [
  {
    id: 1,
    icon: Bot,
    title: "AI Chatbot Development",
    description: "Custom intelligent chatbots for customer support, lead generation, and user engagement with natural language processing.",
    category: "Customer Service",
    completedProjects: "50+",
    avgTimeSaved: "75%",
    featured: true
  },
  {
    id: 2,
    icon: Workflow,
    title: "Workflow Automation",
    description: "End-to-end business process automation connecting your tools and systems for seamless operations.",
    category: "Process Optimization",
    completedProjects: "120+",
    avgTimeSaved: "60%",
    featured: true
  },
  {
    id: 3,
    icon: Mail,
    title: "Email Marketing Automation",
    description: "Intelligent email campaigns with personalization, segmentation, and automated follow-up sequences.",
    category: "Marketing",
    completedProjects: "80+",
    avgTimeSaved: "85%",
    featured: true
  },
  {
    id: 4,
    icon: Database,
    title: "Data Processing & Analytics",
    description: "Automated data collection, processing, and reporting systems with real-time insights and dashboards.",
    category: "Data & Analytics",
    completedProjects: "65+",
    avgTimeSaved: "90%",
    featured: true
  },
  {
    id: 5,
    icon: Target,
    title: "Lead Generation Systems",
    description: "Automated lead capture, qualification, and nurturing systems that convert prospects into customers.",
    category: "Sales",
    completedProjects: "95+",
    avgTimeSaved: "70%",
    featured: true
  },
  {
    id: 6,
    icon: Users,
    title: "CRM Integration & Automation",
    description: "Seamless CRM automation with contact management, pipeline tracking, and automated follow-ups.",
    category: "Customer Management",
    completedProjects: "75+",
    avgTimeSaved: "65%",
    featured: true
  }
];

export function ServicesSection() {
  const getGridArea = (index: number): string => {
    const areas = [
      "md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]",
      "md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]",
      "md:[grid-area:2/1/3/7] xl:[grid-area:1/5/2/9]",
      "md:[grid-area:2/7/3/13] xl:[grid-area:1/9/2/13]",
      "md:[grid-area:3/1/4/7] xl:[grid-area:2/5/3/9]",
      "md:[grid-area:3/7/4/13] xl:[grid-area:2/9/3/13]",
    ];
    return areas[index] || "";
  };

  return (
    <section id="services" className="pt-8 md:pt-16 pb-16 md:pb-32 bg-black/[0.96] relative w-full overflow-hidden">
      {/* Enhanced ambient background for services */}
      <div className="absolute inset-0 ambient-gradient-overlay-center opacity-60" />
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-yellow-400/5 via-orange-500/4 to-transparent rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-tl from-red-500/4 via-orange-500/3 to-transparent rounded-full blur-3xl" />

      <div className="container mx-auto space-y-12 relative z-10">
        {/* Section Header */}
        <div className="relative z-10 grid items-center gap-6 md:grid-cols-2 md:gap-12">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-semibold bg-clip-text text-transparent bg-gradient-to-b from-neutral-50 to-neutral-400">
            Our Services
          </h2>
          <p className="max-w-lg sm:ml-auto text-neutral-300 leading-relaxed text-lg md:text-xl">
            Comprehensive AI automation solutions designed to transform your business operations, increase efficiency, and drive growth across all departments.
          </p>
        </div>

        {/* Services Grid */}
        <div className="relative z-10">
          <ul className="grid grid-cols-1 grid-rows-none gap-4 md:grid-cols-12 md:grid-rows-3 lg:gap-6 xl:max-h-[36rem] xl:grid-rows-2">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <ServiceItem
                  key={service.id}
                  area={getGridArea(index)}
                  icon={<IconComponent className="h-5 w-5" />}
                  title={service.title}
                  description={service.description}
                  category={service.category}
                  completedProjects={service.completedProjects}
                  avgTimeSaved={service.avgTimeSaved}
                />
              );
            })}
          </ul>
        </div>
      </div>
    </section>
  );
}

interface ServiceItemProps {
  area: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  category: string;
  completedProjects: string;
  avgTimeSaved: string;
}

const ServiceItem = ({
  area,
  icon,
  title,
  description,
  category,
  completedProjects,
  avgTimeSaved
}: ServiceItemProps) => {
  return (
    <li className={cn("min-h-[16rem] list-none", area)}>
      <div className="relative h-full rounded-[1.25rem] border-[0.75px] border-neutral-800/50 p-2 md:rounded-[1.5rem] md:p-3 hover:border-neutral-700/50 transition-all duration-300 group cursor-pointer">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
          borderWidth={2}
        />
        <div className="relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border-[0.75px] card-ambient-glow p-6 shadow-lg hover:shadow-2xl transition-all duration-300">
          {/* Subtle brand-colored ambient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/2 via-orange-500/3 to-red-500/2 rounded-xl opacity-60" />

          <div className="relative flex flex-1 flex-col justify-between gap-4">
            {/* Icon and Category */}
            <div className="flex items-center justify-between">
              <div className="w-fit rounded-lg border-[0.75px] border-neutral-700/50 bg-gradient-to-br from-neutral-800/60 to-neutral-700/40 p-3">
                <div className="text-neutral-300">
                  {icon}
                </div>
              </div>
              <div className="flex flex-col items-end gap-1">
                <div className="text-xs text-neutral-500 font-medium">
                  {category}
                </div>
                <span className="text-xs bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full font-medium">
                  CUSTOM
                </span>
              </div>
            </div>

            {/* Content */}
            <div className="space-y-3 flex-1">
              <h3 className="pt-0.5 text-xl leading-[1.375rem] font-semibold font-sans tracking-[-0.04em] md:text-2xl md:leading-[1.875rem] text-balance text-neutral-50">
                {title}
              </h3>
              <p className="font-sans text-sm leading-[1.125rem] md:text-base md:leading-[1.375rem] text-neutral-400">
                {description}
              </p>
            </div>

            {/* Stats */}
            <div className="flex items-center justify-between pt-2 border-t border-neutral-800/50">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-neutral-500">Completed</span>
                  <span className="text-sm font-medium text-neutral-300">{completedProjects}</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-neutral-500">Time Saved</span>
                  <span className="text-sm font-medium text-green-400">{avgTimeSaved}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </li>
  );
};
